import { Head, Link, useForm } from '@inertiajs/react';
import { LoaderCircle, Package, User, Shield } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import MarketingLayout from '@/layouts/marketing-layout';

type LoginForm = {
    email: string;
    password: string;
    remember: boolean;
};

interface LoginProps {
    status?: string;
    canResetPassword: boolean;
}

export default function Login({ status, canResetPassword }: LoginProps) {
    const { data, setData, post, processing, errors, reset } = useForm<Required<LoginForm>>({
        email: '',
        password: '',
        remember: false,
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('login'), {
            onFinish: () => reset('password'),
        });
    };

    return (
        <MarketingLayout>
            <Head title="Sign In - RT Express" />

            <div className="min-h-screen bg-gray-50 py-12">
                <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="text-center mb-8">
                        <div className="bg-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Package className="h-8 w-8 text-white" />
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Welcome Back
                        </h1>
                        <p className="text-gray-600">
                            Sign in to your RT Express account
                        </p>
                    </div>

                    {/* Login Form */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Sign In</CardTitle>
                            <CardDescription>
                                Enter your credentials to access your account
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form className="space-y-6" onSubmit={submit}>
                                {status && (
                                    <div className="text-center text-sm text-green-600 bg-green-50 p-3 rounded-lg">
                                        {status}
                                    </div>
                                )}

                                <div className="space-y-4">
                                    <div>
                                        <Label htmlFor="email">Email Address</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            required
                                            autoFocus
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            className={errors.email ? 'border-red-500' : ''}
                                            placeholder="<EMAIL>"
                                        />
                                        <InputError message={errors.email} />
                                    </div>

                                    <div>
                                        <div className="flex items-center justify-between">
                                            <Label htmlFor="password">Password</Label>
                                            {canResetPassword && (
                                                <TextLink href={route('password.request')} className="text-sm text-blue-600 hover:text-blue-800">
                                                    Forgot password?
                                                </TextLink>
                                            )}
                                        </div>
                                        <Input
                                            id="password"
                                            type="password"
                                            required
                                            value={data.password}
                                            onChange={(e) => setData('password', e.target.value)}
                                            className={errors.password ? 'border-red-500' : ''}
                                            placeholder="Password"
                                        />
                                        <InputError message={errors.password} />
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="remember"
                                            checked={data.remember}
                                            onClick={() => setData('remember', !data.remember)}
                                        />
                                        <Label htmlFor="remember" className="text-sm">
                                            Remember me
                                        </Label>
                                    </div>
                                </div>

                                <Button type="submit" disabled={processing} className="w-full">
                                    {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                                    Sign In
                                </Button>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Register Link */}
                    <div className="text-center mt-6">
                        <p className="text-gray-600">
                            Don't have an account?{' '}
                            <Link href="/register/customer" className="text-blue-600 hover:text-blue-800 font-medium">
                                Create a customer account
                            </Link>
                        </p>
                    </div>

                    {/* Demo Accounts */}
                    <Card className="mt-6 bg-blue-50 border-blue-200">
                        <CardHeader>
                            <CardTitle className="text-sm">Demo Accounts</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2 text-xs">
                            <div>
                                <strong>Customer:</strong> <EMAIL> / sarah123
                            </div>
                            <div>
                                <strong>Admin:</strong> <EMAIL> / admin123
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </MarketingLayout>
    );
}
