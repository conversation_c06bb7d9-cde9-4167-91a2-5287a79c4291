import { PlaceholderPattern } from '@/components/ui/placeholder-pattern';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import {
    MobileDashboardGrid,
    MobileKPICard,
    MobileChartCard,
    MobileActivityFeed,
    MobileQuickActions
} from '@/components/ui/mobile-dashboard';
import {
    Package,
    Truck,
    Users,
    DollarSign,
    Plus,
    Search,
    BarChart3,
    MapPin
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard() {
    // Sample data - in real app, this would come from props or API
    const kpiData = [
        {
            title: "Total Shipments",
            value: "1,234",
            change: { value: 12, type: 'increase' as const, period: 'vs last month' },
            icon: Package,
            description: "Active shipments"
        },
        {
            title: "In Transit",
            value: "456",
            change: { value: 5, type: 'increase' as const, period: 'vs last week' },
            icon: Truck,
            description: "Currently shipping"
        },
        {
            title: "Customers",
            value: "89",
            change: { value: 3, type: 'decrease' as const, period: 'vs last month' },
            icon: Users,
            description: "Active customers"
        },
        {
            title: "Revenue",
            value: "$45,678",
            change: { value: 18, type: 'increase' as const, period: 'vs last month' },
            icon: DollarSign,
            description: "Monthly revenue"
        }
    ];

    const quickActions = [
        {
            label: "Create Shipment",
            icon: Plus,
            onClick: () => window.location.href = '/admin/shipments/create',
            color: 'default' as const
        },
        {
            label: "Track Package",
            icon: Search,
            onClick: () => window.location.href = '/admin/tracking',
            color: 'default' as const
        },
        {
            label: "View Reports",
            icon: BarChart3,
            onClick: () => window.location.href = '/admin/reports',
            color: 'default' as const
        },
        {
            label: "Live Tracking",
            icon: MapPin,
            onClick: () => window.location.href = '/admin/tracking/live',
            color: 'default' as const
        }
    ];

    const recentActivity = [
        {
            id: '1',
            title: 'New shipment created',
            description: 'RT123456789 - Express delivery to New York',
            timestamp: '2 minutes ago',
            type: 'info' as const
        },
        {
            id: '2',
            title: 'Package delivered',
            description: 'RT123456788 - Successfully delivered to customer',
            timestamp: '15 minutes ago',
            type: 'success' as const
        },
        {
            id: '3',
            title: 'Delivery delayed',
            description: 'RT123456787 - Weather conditions causing delay',
            timestamp: '1 hour ago',
            type: 'warning' as const
        }
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="space-y-4 sm:space-y-6 p-4 md:p-6">
                {/* Mobile-First Header */}
                <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                    <div>
                        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">
                            Dashboard
                        </h1>
                        <p className="text-sm sm:text-base text-muted-foreground">
                            Welcome back! Here's what's happening with your shipments.
                        </p>
                    </div>
                </div>

                {/* KPI Cards - Mobile-First Grid */}
                <MobileDashboardGrid columns={2}>
                    {kpiData.map((kpi, index) => (
                        <MobileKPICard
                            key={index}
                            title={kpi.title}
                            value={kpi.value}
                            change={kpi.change}
                            icon={kpi.icon}
                            description={kpi.description}
                            onClick={() => console.log(`Clicked ${kpi.title}`)}
                        />
                    ))}
                </MobileDashboardGrid>

                {/* Quick Actions */}
                <MobileQuickActions
                    title="Quick Actions"
                    actions={quickActions}
                    columns={2}
                />

                {/* Charts and Activity - Mobile-First Layout */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-2">
                    <MobileChartCard
                        title="Shipment Trends"
                        description="Monthly shipment volume"
                        height="md"
                    >
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                                <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <p className="text-sm text-muted-foreground">
                                    Chart component will be integrated here
                                </p>
                            </div>
                        </div>
                    </MobileChartCard>

                    <MobileActivityFeed
                        title="Recent Activity"
                        items={recentActivity}
                        maxItems={5}
                        showViewAll={true}
                        onViewAll={() => console.log('View all activity')}
                    />
                </div>
            </div>
        </AppLayout>
    );
}
