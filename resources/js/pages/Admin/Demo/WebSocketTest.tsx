import { Head } from '@inertiajs/react';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ConnectionStatus } from '@/components/ui/real-time-indicator';
import { usePusher, useShipmentTracking, useRouteTracking, useDashboardStats } from '@/hooks/usePusher';
import { 
    Zap,
    Send,
    Package,
    Truck,
    BarChart3,
    Activity,
    CheckCircle,
    AlertTriangle,
    Wifi,
    WifiOff
} from 'lucide-react';

export default function WebSocketTest() {
    const [testMessage, setTestMessage] = useState('');
    const [testResults, setTestResults] = useState<any[]>([]);
    
    // Multiple WebSocket connections for testing
    const mainConnection = usePusher({
        onMessage: (message) => {
            setTestResults(prev => [{
                id: Date.now(),
                type: 'main',
                message,
                timestamp: new Date().toISOString()
            }, ...prev.slice(0, 9)]);
        }
    });

    const shipmentTracking = useShipmentTracking('RT001234567');
    const routeTracking = useRouteTracking('1');
    const dashboardStats = useDashboardStats();

    const triggerShipmentUpdate = async () => {
        try {
            const response = await fetch('/admin/websocket/test/shipment-update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    status: 'in_transit',
                    location: 'Los Angeles, CA'
                })
            });
            
            const result = await response.json();
            console.log('Shipment update triggered:', result);
            
            setTestResults(prev => [{
                id: Date.now(),
                type: 'trigger',
                message: { type: 'shipment_trigger', data: result },
                timestamp: new Date().toISOString()
            }, ...prev.slice(0, 9)]);
        } catch (error) {
            console.error('Failed to trigger shipment update:', error);
        }
    };

    const triggerRouteUpdate = async () => {
        try {
            const response = await fetch('/admin/websocket/test/route-update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    progress: Math.floor(Math.random() * 100),
                    current_stop: 'Test Stop ' + Math.floor(Math.random() * 10)
                })
            });
            
            const result = await response.json();
            console.log('Route update triggered:', result);
            
            setTestResults(prev => [{
                id: Date.now(),
                type: 'trigger',
                message: { type: 'route_trigger', data: result },
                timestamp: new Date().toISOString()
            }, ...prev.slice(0, 9)]);
        } catch (error) {
            console.error('Failed to trigger route update:', error);
        }
    };

    const triggerStatsUpdate = async () => {
        try {
            const response = await fetch('/admin/websocket/test/stats-update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });
            
            const result = await response.json();
            console.log('Stats update triggered:', result);
            
            setTestResults(prev => [{
                id: Date.now(),
                type: 'trigger',
                message: { type: 'stats_trigger', data: result },
                timestamp: new Date().toISOString()
            }, ...prev.slice(0, 9)]);
        } catch (error) {
            console.error('Failed to trigger stats update:', error);
        }
    };

    const sendCustomMessage = async () => {
        if (!testMessage.trim()) return;
        
        try {
            const response = await fetch('/admin/websocket/test/custom-event', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    type: 'custom_test',
                    message: testMessage,
                    data: { sender: 'WebSocket Test Page' }
                })
            });
            
            const result = await response.json();
            console.log('Custom message sent:', result);
            setTestMessage('');
            
            setTestResults(prev => [{
                id: Date.now(),
                type: 'trigger',
                message: { type: 'custom_trigger', data: result },
                timestamp: new Date().toISOString()
            }, ...prev.slice(0, 9)]);
        } catch (error) {
            console.error('Failed to send custom message:', error);
        }
    };

    const getConnectionStatus = (isConnected: boolean) => {
        return isConnected ? (
            <Badge variant="success" className="flex items-center space-x-1">
                <Wifi className="h-3 w-3" />
                <span>Connected</span>
            </Badge>
        ) : (
            <Badge variant="destructive" className="flex items-center space-x-1">
                <WifiOff className="h-3 w-3" />
                <span>Disconnected</span>
            </Badge>
        );
    };

    return (
        <AppLayout>
            <Head title="WebSocket Test" />
            
            <div className="space-y-6 p-4 md:p-6">
                {/* Header */}
                <div className="text-center space-y-4">
                    <div className="flex items-center justify-center space-x-2">
                        <Zap className="h-8 w-8 text-primary" />
                        <h1 className="text-3xl font-bold tracking-tight">WebSocket Test Center</h1>
                    </div>
                    <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        Test real-time WebSocket connections and broadcasting functionality
                    </p>
                </div>

                {/* Connection Status */}
                <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm">Main Connection</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {getConnectionStatus(mainConnection.isConnected)}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm">Shipment Tracking</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {getConnectionStatus(shipmentTracking.isConnected)}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm">Route Tracking</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {getConnectionStatus(routeTracking.isConnected)}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm">Dashboard Stats</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {getConnectionStatus(dashboardStats.isConnected)}
                        </CardContent>
                    </Card>
                </div>

                {/* Test Controls */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Send className="h-5 w-5" />
                            <span>Test Controls</span>
                        </CardTitle>
                        <CardDescription>
                            Trigger real-time events to test WebSocket functionality
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
                            <Button 
                                onClick={triggerShipmentUpdate}
                                disabled={!mainConnection.isConnected}
                                className="flex items-center space-x-2"
                            >
                                <Package className="h-4 w-4" />
                                <span>Test Shipment Update</span>
                            </Button>

                            <Button 
                                onClick={triggerRouteUpdate}
                                disabled={!mainConnection.isConnected}
                                className="flex items-center space-x-2"
                            >
                                <Truck className="h-4 w-4" />
                                <span>Test Route Update</span>
                            </Button>

                            <Button 
                                onClick={triggerStatsUpdate}
                                disabled={!mainConnection.isConnected}
                                className="flex items-center space-x-2"
                            >
                                <BarChart3 className="h-4 w-4" />
                                <span>Test Stats Update</span>
                            </Button>
                        </div>

                        <Separator />

                        <div className="space-y-2">
                            <Label htmlFor="custom-message">Custom Message</Label>
                            <div className="flex space-x-2">
                                <Input
                                    id="custom-message"
                                    placeholder="Enter custom test message..."
                                    value={testMessage}
                                    onChange={(e) => setTestMessage(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && sendCustomMessage()}
                                />
                                <Button 
                                    onClick={sendCustomMessage}
                                    disabled={!mainConnection.isConnected || !testMessage.trim()}
                                >
                                    Send
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Real-time Events Log */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Activity className="h-5 w-5" />
                            <span>Real-time Events Log</span>
                        </CardTitle>
                        <CardDescription>
                            Live feed of WebSocket events and messages
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3 max-h-96 overflow-y-auto">
                            {testResults.length > 0 ? testResults.map((result) => (
                                <div
                                    key={result.id}
                                    className={`p-3 rounded-lg border ${
                                        result.type === 'trigger' 
                                            ? 'bg-blue-50 border-blue-200' 
                                            : 'bg-green-50 border-green-200'
                                    }`}
                                >
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center space-x-2 mb-2">
                                                {result.type === 'trigger' ? (
                                                    <Send className="h-4 w-4 text-blue-600" />
                                                ) : (
                                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                                )}
                                                <Badge variant="outline" className="text-xs">
                                                    {result.message.type}
                                                </Badge>
                                                <span className="text-xs text-muted-foreground">
                                                    {new Date(result.timestamp).toLocaleTimeString()}
                                                </span>
                                            </div>
                                            <pre className="text-xs bg-white p-2 rounded border overflow-x-auto">
                                                {JSON.stringify(result.message, null, 2)}
                                            </pre>
                                        </div>
                                    </div>
                                </div>
                            )) : (
                                <div className="text-center py-8 text-muted-foreground">
                                    <Activity className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                                    <p>No events yet. Trigger some tests to see real-time updates!</p>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Instructions */}
                <Card>
                    <CardHeader>
                        <CardTitle>How to Test</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                        <div className="text-sm space-y-2">
                            <p><strong>1. Start Laravel Reverb:</strong> Run <code className="bg-muted px-1 rounded">php artisan reverb:start</code> in your terminal</p>
                            <p><strong>2. Check Connections:</strong> Ensure all connection status badges show "Connected"</p>
                            <p><strong>3. Trigger Events:</strong> Click the test buttons to send real-time events</p>
                            <p><strong>4. Watch the Log:</strong> See events appear in real-time in the events log below</p>
                            <p><strong>5. Test Custom Messages:</strong> Send custom messages using the input field</p>
                        </div>
                        
                        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div className="flex items-start space-x-2">
                                <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                                <div className="text-sm text-yellow-800">
                                    <p className="font-medium">Note:</p>
                                    <p>Make sure Laravel Reverb is running on port 8080 for WebSocket connections to work properly.</p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
