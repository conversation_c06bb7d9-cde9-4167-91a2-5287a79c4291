import { Head } from '@inertiajs/react';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { FileUpload } from '@/components/ui/file-upload';
import { AdvancedFilters } from '@/components/ui/advanced-filters';
import { BulkOperations, commonBulkActions } from '@/components/ui/bulk-operations';
import { ConnectionStatus, LiveDataIndicator, NotificationPermission } from '@/components/ui/real-time-indicator';
import { usePusher } from '@/hooks/usePusher';
import { 
    Sparkles,
    Upload,
    Filter,
    CheckSquare,
    Wifi,
    FileText,
    Users,
    Package,
    Truck,
    Bell
} from 'lucide-react';

export default function AdvancedFeaturesDemo() {
    const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
    const [filterValues, setFilterValues] = useState({});
    const [selectedItems, setSelectedItems] = useState<string[]>([]);
    
    // Pusher connection
    const { isConnected, lastMessage } = usePusher({
        onMessage: (message) => {
            console.log('Received message:', message);
        }
    });

    // Sample data for demonstrations
    const sampleItems = [
        { id: '1', name: 'Shipment #SH001', status: 'In Transit' },
        { id: '2', name: 'Shipment #SH002', status: 'Delivered' },
        { id: '3', name: 'Shipment #SH003', status: 'Pending' },
        { id: '4', name: 'Shipment #SH004', status: 'Exception' },
        { id: '5', name: 'Shipment #SH005', status: 'In Transit' }
    ];

    // Filter configurations for demo
    const filterConfigs = [
        {
            key: 'search',
            label: 'Search',
            type: 'text' as const,
            placeholder: 'Search shipments...'
        },
        {
            key: 'status',
            label: 'Status',
            type: 'multiselect' as const,
            options: [
                { value: 'pending', label: 'Pending', count: 12 },
                { value: 'in_transit', label: 'In Transit', count: 25 },
                { value: 'delivered', label: 'Delivered', count: 156 },
                { value: 'exception', label: 'Exception', count: 3 }
            ]
        },
        {
            key: 'date_range',
            label: 'Date Range',
            type: 'daterange' as const
        },
        {
            key: 'priority',
            label: 'Priority',
            type: 'select' as const,
            options: [
                { value: 'low', label: 'Low' },
                { value: 'medium', label: 'Medium' },
                { value: 'high', label: 'High' },
                { value: 'urgent', label: 'Urgent' }
            ]
        }
    ];

    const handleFileUpload = (files: File[]) => {
        setUploadedFiles(files);
        console.log('Files uploaded:', files);
    };

    const handleFilterChange = (filters: any) => {
        setFilterValues(filters);
        console.log('Filters changed:', filters);
    };

    const handleSelectAll = () => {
        setSelectedItems(sampleItems.map(item => item.id));
    };

    const handleSelectNone = () => {
        setSelectedItems([]);
    };

    const handleToggleItem = (id: string) => {
        setSelectedItems(prev => 
            prev.includes(id) 
                ? prev.filter(item => item !== id)
                : [...prev, id]
        );
    };

    const handleBulkAction = async (actionId: string, selectedIds: string[]) => {
        console.log(`Executing ${actionId} on items:`, selectedIds);
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        alert(`Successfully executed ${actionId} on ${selectedIds.length} items!`);
        setSelectedItems([]);
    };

    return (
        <AppLayout>
            <Head title="Advanced Features Demo" />
            
            <div className="space-y-8 p-4 md:p-6">
                {/* Header */}
                <div className="text-center space-y-4">
                    <div className="flex items-center justify-center space-x-2">
                        <Sparkles className="h-8 w-8 text-primary" />
                        <h1 className="text-3xl font-bold tracking-tight">Advanced Features Demo</h1>
                    </div>
                    <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        Experience the cutting-edge features that make RT Express Admin Dashboard 
                        a truly enterprise-grade solution.
                    </p>
                </div>

                {/* Real-time Connection Status */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Wifi className="h-5 w-5" />
                            <span>Real-time Updates</span>
                        </CardTitle>
                        <CardDescription>
                            WebSocket integration for live tracking and notifications
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex flex-wrap items-center gap-4">
                            <ConnectionStatus />
                            <NotificationPermission />
                            <LiveDataIndicator 
                                lastUpdated={new Date()} 
                                isLive={isConnected} 
                            />
                        </div>
                        
                        <div className="p-4 bg-muted rounded-lg">
                            <h4 className="font-medium mb-2">Real-time Features:</h4>
                            <ul className="text-sm text-muted-foreground space-y-1">
                                <li>• Live shipment status updates</li>
                                <li>• Real-time route tracking</li>
                                <li>• Instant notifications for critical events</li>
                                <li>• Automatic data refresh without page reload</li>
                                <li>• Browser push notifications</li>
                            </ul>
                        </div>

                        {lastMessage && (
                            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                                <p className="text-sm font-medium text-blue-800">Last Message:</p>
                                <p className="text-sm text-blue-700">{JSON.stringify(lastMessage, null, 2)}</p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Enhanced File Upload */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Upload className="h-5 w-5" />
                            <span>Enhanced File Upload</span>
                        </CardTitle>
                        <CardDescription>
                            Drag & drop file upload with progress bars, preview, and validation
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <FileUpload
                            onFilesChange={handleFileUpload}
                            maxFiles={5}
                            maxSize={10}
                            acceptedTypes={['image/*', 'application/pdf', '.doc', '.docx']}
                            showPreview={true}
                        />
                        
                        <div className="mt-4 p-4 bg-muted rounded-lg">
                            <h4 className="font-medium mb-2">Upload Features:</h4>
                            <ul className="text-sm text-muted-foreground space-y-1">
                                <li>• Drag & drop interface</li>
                                <li>• Real-time upload progress</li>
                                <li>• File type validation</li>
                                <li>• Image preview thumbnails</li>
                                <li>• File size limits</li>
                                <li>• Multiple file selection</li>
                            </ul>
                        </div>
                    </CardContent>
                </Card>

                {/* Advanced Filtering */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Filter className="h-5 w-5" />
                            <span>Advanced Filtering</span>
                        </CardTitle>
                        <CardDescription>
                            Powerful filtering with date ranges, multi-select, and saved presets
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <AdvancedFilters
                            configs={filterConfigs}
                            values={filterValues}
                            onChange={handleFilterChange}
                        />
                        
                        <div className="mt-4 p-4 bg-muted rounded-lg">
                            <h4 className="font-medium mb-2">Filter Features:</h4>
                            <ul className="text-sm text-muted-foreground space-y-1">
                                <li>• Multiple filter types (text, select, multiselect, date ranges)</li>
                                <li>• Real-time filter application</li>
                                <li>• Filter count indicators</li>
                                <li>• Save and load filter presets</li>
                                <li>• Clear individual or all filters</li>
                                <li>• Mobile-responsive design</li>
                            </ul>
                        </div>

                        {Object.keys(filterValues).length > 0 && (
                            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                                <p className="text-sm font-medium text-blue-800">Current Filters:</p>
                                <pre className="text-sm text-blue-700 mt-1">
                                    {JSON.stringify(filterValues, null, 2)}
                                </pre>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Bulk Operations */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <CheckSquare className="h-5 w-5" />
                            <span>Bulk Operations</span>
                        </CardTitle>
                        <CardDescription>
                            Select multiple items and perform batch actions with progress tracking
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <BulkOperations
                            selectedItems={selectedItems}
                            totalItems={sampleItems.length}
                            onSelectAll={handleSelectAll}
                            onSelectNone={handleSelectNone}
                            onToggleItem={handleToggleItem}
                            actions={commonBulkActions}
                            onAction={handleBulkAction}
                        />

                        {/* Sample Items List */}
                        <div className="border rounded-lg overflow-hidden">
                            <div className="bg-muted px-4 py-2">
                                <h4 className="font-medium">Sample Items</h4>
                            </div>
                            <div className="divide-y">
                                {sampleItems.map((item) => (
                                    <div key={item.id} className="flex items-center space-x-3 p-4">
                                        <input
                                            type="checkbox"
                                            checked={selectedItems.includes(item.id)}
                                            onChange={() => handleToggleItem(item.id)}
                                            className="rounded"
                                        />
                                        <Package className="h-4 w-4 text-muted-foreground" />
                                        <div className="flex-1">
                                            <p className="font-medium">{item.name}</p>
                                        </div>
                                        <Badge variant="outline">{item.status}</Badge>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="p-4 bg-muted rounded-lg">
                            <h4 className="font-medium mb-2">Bulk Operation Features:</h4>
                            <ul className="text-sm text-muted-foreground space-y-1">
                                <li>• Select all/none functionality</li>
                                <li>• Individual item selection</li>
                                <li>• Batch action dropdown</li>
                                <li>• Confirmation dialogs for destructive actions</li>
                                <li>• Progress tracking during execution</li>
                                <li>• Automatic deselection after completion</li>
                            </ul>
                        </div>
                    </CardContent>
                </Card>

                {/* Feature Summary */}
                <Card>
                    <CardHeader>
                        <CardTitle>🎉 Advanced Features Summary</CardTitle>
                        <CardDescription>
                            All features are now integrated and ready for production use
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                            <div className="space-y-3">
                                <div className="flex items-center space-x-2">
                                    <Badge variant="success">✓</Badge>
                                    <span className="text-sm">Real-time WebSocket Integration</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Badge variant="success">✓</Badge>
                                    <span className="text-sm">Enhanced File Upload System</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Badge variant="success">✓</Badge>
                                    <span className="text-sm">Advanced Filtering System</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Badge variant="success">✓</Badge>
                                    <span className="text-sm">Bulk Operations Interface</span>
                                </div>
                            </div>
                            <div className="space-y-3">
                                <div className="flex items-center space-x-2">
                                    <Badge variant="success">✓</Badge>
                                    <span className="text-sm">Live Status Indicators</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Badge variant="success">✓</Badge>
                                    <span className="text-sm">Browser Push Notifications</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Badge variant="success">✓</Badge>
                                    <span className="text-sm">Progress Tracking</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Badge variant="success">✓</Badge>
                                    <span className="text-sm">Mobile-Responsive Design</span>
                                </div>
                            </div>
                        </div>

                        <Separator className="my-6" />

                        <div className="text-center">
                            <p className="text-lg font-medium text-green-600 mb-2">
                                🚀 RT Express Admin Dashboard - Enterprise Ready!
                            </p>
                            <p className="text-sm text-muted-foreground">
                                All advanced features are now implemented and ready for production deployment.
                            </p>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
